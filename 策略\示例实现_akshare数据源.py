"""
妖底确定买入高抛卖出策略 - AKShare数据源示例实现
展示如何使用AKShare作为数据源实现通用版策略

注意：这只是一个示例实现，实际使用时需要：
1. 安装akshare: pip install akshare
2. 实现真实的交易接口
3. 添加适当的错误处理和数据缓存
"""

import akshare as ak
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import List, Dict
import logging

# 导入通用策略类
from 妖底确定买入高抛卖出策略_通用版 import DataProvider, TradeProvider, YaodiStrategy, StrategyConfig

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AKShareDataProvider(DataProvider):
    """基于AKShare的数据提供者实现"""
    
    def __init__(self):
        self.stock_list_cache = None
        self.data_cache = {}  # 简单的数据缓存
        
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        """
        获取股票历史数据
        :param symbol: 股票代码，如 '000001' 或 '000001.SZ'
        :param count: 获取的数据条数
        :return: 包含 'close', 'high', 'low', 'volume' 列的DataFrame
        """
        try:
            # 转换股票代码格式
            stock_code = self._convert_symbol(symbol)
            
            # 检查缓存
            cache_key = f"{stock_code}_{count}"
            if cache_key in self.data_cache:
                cached_data, cache_time = self.data_cache[cache_key]
                # 如果缓存时间不超过1小时，使用缓存
                if (datetime.now() - cache_time).seconds < 3600:
                    return cached_data
            
            # 获取历史数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=count * 2)).strftime('%Y%m%d')
            
            # 使用akshare获取股票历史数据
            df = ak.stock_zh_a_hist(symbol=stock_code, period="daily", 
                                   start_date=start_date, end_date=end_date, adjust="qfq")
            
            if df is None or len(df) == 0:
                return pd.DataFrame()
            
            # 重命名列以匹配策略需要的格式
            df = df.rename(columns={
                '收盘': 'close',
                '最高': 'high', 
                '最低': 'low',
                '成交量': 'volume'
            })
            
            # 确保数据类型正确
            for col in ['close', 'high', 'low', 'volume']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 取最近count条数据
            df = df.tail(count).reset_index(drop=True)
            
            # 缓存数据
            self.data_cache[cache_key] = (df, datetime.now())
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def get_current_price(self, symbol: str) -> float:
        """
        获取当前价格
        :param symbol: 股票代码
        :return: 当前价格
        """
        try:
            stock_code = self._convert_symbol(symbol)
            
            # 获取实时数据
            df = ak.stock_zh_a_spot_em()
            if df is None or len(df) == 0:
                return 0.0
                
            # 查找对应股票
            stock_data = df[df['代码'] == stock_code]
            if len(stock_data) == 0:
                return 0.0
                
            return float(stock_data['最新价'].iloc[0])
            
        except Exception as e:
            logger.error(f"获取当前价格失败 {symbol}: {e}")
            return 0.0
    
    def get_stock_list(self) -> List[str]:
        """
        获取股票列表
        :return: 股票代码列表
        """
        try:
            if self.stock_list_cache is not None:
                return self.stock_list_cache
                
            # 获取A股股票列表
            df = ak.stock_zh_a_spot_em()
            if df is None or len(df) == 0:
                return []
            
            # 过滤股票
            stock_list = []
            for _, row in df.iterrows():
                code = row['代码']
                name = row['名称']
                
                # 排除ST股票
                if 'ST' in name or '*ST' in name:
                    continue
                    
                # 排除北证股票
                if code.startswith('8') or code.startswith('43'):
                    continue
                    
                # 转换为标准格式
                if code.startswith('0') or code.startswith('3'):
                    stock_list.append(f"{code}.XSHE")
                elif code.startswith('6'):
                    stock_list.append(f"{code}.XSHG")
            
            self.stock_list_cache = stock_list
            logger.info(f"获取股票列表完成，共{len(stock_list)}只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def _convert_symbol(self, symbol: str) -> str:
        """转换股票代码格式"""
        if '.' in symbol:
            return symbol.split('.')[0]
        return symbol


class MockTradeProvider(TradeProvider):
    """模拟交易提供者（仅用于测试）"""
    
    def __init__(self, initial_cash: float = 1000000):
        self.positions = {}  # {symbol: {'amount': float, 'avg_cost': float}}
        self.available_cash = initial_cash
        self.total_value = initial_cash
        
    def buy(self, symbol: str, amount: float) -> bool:
        """模拟买入"""
        try:
            # 这里应该实现真实的买入逻辑
            logger.info(f"模拟买入 {symbol}: {amount} 股")
            return True
        except Exception as e:
            logger.error(f"买入失败 {symbol}: {e}")
            return False
    
    def sell(self, symbol: str, amount: float) -> bool:
        """模拟卖出"""
        try:
            # 这里应该实现真实的卖出逻辑
            logger.info(f"模拟卖出 {symbol}: {amount} 股")
            return True
        except Exception as e:
            logger.error(f"卖出失败 {symbol}: {e}")
            return False
    
    def get_positions(self) -> Dict[str, Dict]:
        """获取持仓信息"""
        return self.positions
    
    def get_account_info(self) -> Dict:
        """获取账户信息"""
        return {
            'available_cash': self.available_cash,
            'total_value': self.total_value
        }


def main():
    """主函数 - 演示如何使用策略"""
    
    print("妖底确定买入高抛卖出策略 - AKShare数据源示例")
    print("=" * 50)
    
    try:
        # 创建数据提供者和交易提供者
        data_provider = AKShareDataProvider()
        trade_provider = MockTradeProvider(initial_cash=1000000)
        
        # 创建策略配置
        config = StrategyConfig()
        config.position_ratio = 0.1  # 10%仓位
        
        # 创建策略实例
        strategy = YaodiStrategy(data_provider, trade_provider, config)
        
        # 初始化策略
        strategy.initialize()
        
        print("策略初始化完成")
        
        # 测试获取股票池（限制数量以加快测试）
        print("正在获取股票池...")
        stock_pool = strategy.get_stock_pool()
        print(f"股票池大小: {len(stock_pool)}")
        
        if len(stock_pool) > 0:
            # 测试计算信号（只测试前几只股票）
            test_stocks = stock_pool[:5]
            print(f"测试计算信号，股票: {test_stocks}")
            
            for stock in test_stocks:
                print(f"正在计算 {stock} 的信号...")
                try:
                    yaodi_signal = strategy.calculate_yaodi_signal(stock)
                    sell_signal = strategy.calculate_sell_signal(stock)
                    current_price = data_provider.get_current_price(stock)
                    
                    print(f"{stock}: 妖底信号={yaodi_signal}, 卖出信号={sell_signal}, 当前价格={current_price}")
                except Exception as e:
                    print(f"{stock}: 计算信号失败 - {e}")
        
        print("\n策略测试完成")
        print("注意：这只是一个示例实现，实际使用时需要：")
        print("1. 实现真实的交易接口")
        print("2. 添加更完善的错误处理")
        print("3. 实现数据缓存机制")
        print("4. 添加风险控制模块")
        
    except Exception as e:
        logger.error(f"策略运行失败: {e}")


if __name__ == "__main__":
    main()
