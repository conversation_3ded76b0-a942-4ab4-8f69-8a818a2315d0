# 妖底确定买入高抛卖出策略 - 通用版本使用说明

## 概述

本策略已从聚宽平台特定版本改造为通用版本，移除了对 `jqdata` 等平台特定库的依赖，使用抽象接口设计，可以适配不同的数据源和交易平台。

## 主要改动

### 移除的依赖
- `jqdata` - 聚宽数据接口
- `get_bars()` - 聚宽历史数据获取函数
- `get_current_data()` - 聚宽实时数据获取函数
- `order_target()`, `order_value()` - 聚宽交易函数
- `context.portfolio` - 聚宽投资组合对象
- `log` - 聚宽日志系统

### 新增的通用接口

#### 1. DataProvider 抽象类
负责提供股票数据，需要实现以下方法：
- `get_stock_data(symbol, count)` - 获取历史数据
- `get_current_price(symbol)` - 获取当前价格
- `get_stock_list()` - 获取股票列表

#### 2. TradeProvider 抽象类
负责执行交易操作，需要实现以下方法：
- `buy(symbol, amount)` - 买入股票
- `sell(symbol, amount)` - 卖出股票
- `get_positions()` - 获取持仓信息
- `get_account_info()` - 获取账户信息

#### 3. StrategyConfig 配置类
包含策略参数配置：
- `position_ratio` - 单次买入仓位比例（默认0.1）
- `trade_time` - 交易时间（默认"14:55"）
- `commission_rate` - 手续费率
- `stamp_tax` - 印花税
- `min_commission` - 最小手续费

## 使用方法

### 1. 实现数据提供者

```python
class MyDataProvider(DataProvider):
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        # 实现获取历史数据的逻辑
        # 返回包含 'close', 'high', 'low', 'volume' 列的DataFrame
        pass
    
    def get_current_price(self, symbol: str) -> float:
        # 实现获取当前价格的逻辑
        pass
    
    def get_stock_list(self) -> List[str]:
        # 实现获取股票列表的逻辑
        # 返回股票代码列表，如 ['000001.XSHE', '000002.XSHE', ...]
        pass
```

### 2. 实现交易提供者

```python
class MyTradeProvider(TradeProvider):
    def buy(self, symbol: str, amount: float) -> bool:
        # 实现买入逻辑
        pass
    
    def sell(self, symbol: str, amount: float) -> bool:
        # 实现卖出逻辑
        pass
    
    def get_positions(self) -> Dict[str, Dict]:
        # 返回持仓信息
        # 格式: {symbol: {'amount': float, 'avg_cost': float}}
        pass
    
    def get_account_info(self) -> Dict:
        # 返回账户信息
        # 格式: {'available_cash': float, 'total_value': float}
        pass
```

### 3. 创建和运行策略

```python
# 创建数据提供者和交易提供者实例
data_provider = MyDataProvider()
trade_provider = MyTradeProvider()

# 创建策略配置（可选，使用默认配置）
config = StrategyConfig()
config.position_ratio = 0.1  # 调整仓位比例

# 创建策略实例
strategy = YaodiStrategy(data_provider, trade_provider, config)

# 初始化策略
strategy.initialize()

# 运行策略（建议在定时任务中每日14:55执行）
strategy.run_daily_strategy()

# 收盘后统计
strategy.after_market_close()
```

## 策略逻辑

策略保持原有逻辑不变：

1. **买入逻辑**：
   - 每日收盘前5分钟(14:55)执行
   - 使用妖底确定选股公式筛选买入标的
   - 每次买入当前总资产的1/10
   - 支持对已持有股票继续加仓
   - 无持仓数量限制

2. **卖出逻辑**：
   - 每日收盘前5分钟检测持仓股票
   - 使用高抛低吸卖出指标判断卖出时机
   - 触发卖出信号时全仓卖出该股票

3. **股票池**：
   - 全市场A股
   - 排除北证股票（8开头、43开头）
   - 排除ST股票
   - 排除停牌股票

## 技术指标

### 妖底确定买入信号
包含多个技术指标的复合信号：
- C1指标：价格相对位置
- M2指标：RSI类似指标
- G1条件：动量突破
- TU条件：价格压制
- SMMA相关指标：趋势判断
- TDJ条件：波动率
- MACD条件：趋势确认
- 等等...

### 高抛低吸卖出信号
基于MJ指标的卖出信号：
- 当MJ指标从80以上跌破80时触发卖出

## 注意事项

1. **数据格式**：确保历史数据DataFrame包含必要的列（close, high, low, volume）
2. **股票代码格式**：建议使用标准格式（如000001.XSHE）
3. **异常处理**：策略已包含基本异常处理，但建议在实现接口时也加入异常处理
4. **日志记录**：策略使用Python标准logging模块，可以根据需要调整日志级别
5. **性能考虑**：由于需要计算大量技术指标，建议对数据获取进行缓存优化

## 依赖库

```
pandas
numpy
talib (可选，用于MACD计算)
```

如果没有安装talib，策略会自动使用EMA差值计算MACD。

## 扩展建议

1. **数据缓存**：实现数据缓存机制，避免重复获取相同数据
2. **并行计算**：对于大量股票的信号计算，可以考虑使用多进程/多线程
3. **风控模块**：添加风险控制模块，如最大回撤控制、单股最大仓位限制等
4. **回测框架**：集成回测框架，方便策略测试和优化
5. **监控告警**：添加策略运行监控和异常告警机制
