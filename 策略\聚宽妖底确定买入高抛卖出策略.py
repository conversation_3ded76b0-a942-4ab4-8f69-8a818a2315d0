"""
妖底确定买入高抛卖出量化策略 - 通用版本
结合妖底确定选股和高抛低吸卖出指标的量化交易策略

策略逻辑：
1. 每日收盘前5分钟(14:55)使用妖底确定选股筛选买入标的
2. 买入当前十分之一仓位，已持有股票可继续加仓
3. 收盘前5分钟检测持仓股票是否触发卖出信号
4. 如有卖出信号则全仓卖出
"""

import pandas as pd
import numpy as np
import logging
import datetime
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Tuple

# 尝试导入talib，如果不可用则使用备用方案
try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False
    print("talib不可用，将使用备用MACD计算方法")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 抽象数据接口类
class DataProvider(ABC):
    """数据提供者抽象基类"""

    @abstractmethod
    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        """获取股票历史数据"""
        pass

    @abstractmethod
    def get_current_price(self, symbol: str) -> float:
        """获取当前价格"""
        pass

    @abstractmethod
    def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        pass

# 抽象交易接口类
class TradeProvider(ABC):
    """交易提供者抽象基类"""

    @abstractmethod
    def buy(self, symbol: str, amount: float) -> bool:
        """买入股票"""
        pass

    @abstractmethod
    def sell(self, symbol: str, amount: float) -> bool:
        """卖出股票"""
        pass

    @abstractmethod
    def get_positions(self) -> Dict[str, Dict]:
        """获取持仓信息"""
        pass

    @abstractmethod
    def get_account_info(self) -> Dict:
        """获取账户信息"""
        pass

# 策略配置类
class StrategyConfig:
    """策略配置"""
    def __init__(self):
        self.position_ratio = 0.1  # 单次买入仓位比例（十分之一）
        self.trade_time = "14:55"  # 交易时间
        self.commission_rate = 0.0003  # 手续费率
        self.stamp_tax = 0.001  # 印花税
        self.min_commission = 5  # 最小手续费

# 策略主类
class YaodiStrategy:
    """妖底确定买入高抛卖出策略"""

    def __init__(self, data_provider: DataProvider, trade_provider: TradeProvider, config: StrategyConfig = None):
        self.data_provider = data_provider
        self.trade_provider = trade_provider
        self.config = config or StrategyConfig()
        self.logger = logging.getLogger(self.__class__.__name__)

    def initialize(self):
        """初始化策略"""
        self.logger.info('妖底确定买入高抛卖出策略初始化')
        self.logger.info(f'策略参数: 单次买入仓位比例={self.config.position_ratio}')

    def run_daily_strategy(self):
        """每日策略执行"""
        current_time = datetime.datetime.now().strftime("%H:%M")
        if current_time == self.config.trade_time:
            self.trade_strategy()

    def after_market_close(self):
        """收盘后运行"""
        self.logger.info('收盘后统计')
        self._log_portfolio_status()

    def get_holding_stocks(self) -> List[str]:
        """
        安全地获取当前持仓股票列表
        """
        positions = self.trade_provider.get_positions()
        holding_stocks = []
        for symbol, position in positions.items():
            if position.get('amount', 0) > 0:
                holding_stocks.append(symbol)
        return holding_stocks

    @staticmethod
    def sma_custom(series, period, weight=1):
        """
        自定义SMA函数，模拟通达信的SMA函数
        SMA(X,N,M) = (M*X + (N-M)*Y)/N，其中Y为上一周期SMA值
        """
        result = pd.Series(index=series.index, dtype=float)
        if len(series) == 0:
            return result

        # 初始值使用简单移动平均
        result.iloc[0] = series.iloc[0]

        for i in range(1, len(series)):
            if pd.isna(series.iloc[i]) or pd.isna(result.iloc[i-1]):
                result.iloc[i] = series.iloc[i]
            else:
                result.iloc[i] = (weight * series.iloc[i] + (period - weight) * result.iloc[i-1]) / period

        return result

    @staticmethod
    def apply_filter(condition_series, period):
        """
        模拟通达信FILTER函数
        FILTER(X,N): 当X条件成立时，将其后N周期内的数据置为0，避免重复信号
        """
        if len(condition_series) == 0:
            return condition_series.copy()

        result = condition_series.copy()
        last_true_index = -period - 1

        for i in range(len(condition_series)):
            if condition_series.iloc[i] and (i - last_true_index) > period:
                last_true_index = i
                result.iloc[i] = True
            else:
                result.iloc[i] = False

        return result

    def calculate_yaodi_signal(self, stock: str) -> bool:
        """
        计算妖底确定买入信号 - 完全按照选股公式实现
        :param stock: 股票代码
        :return: 是否满足妖底确定条件
        """
        try:
            # 获取足够的历史数据（需要更多数据用于MACD计算）
            hist_data = self.data_provider.get_stock_data(stock, 100)

            if len(hist_data) < 75:
                return False

            C = hist_data['close']
            H = hist_data['high']
            L = hist_data['low']

            # 转换为pandas Series便于计算
            C_series = pd.Series(C)
            H_series = pd.Series(H)
            L_series = pd.Series(L)

            # 1. C1指标：((MA(C,30)-L)/MA(C,60))*200
            MA30 = C_series.rolling(30).mean()
            MA60 = C_series.rolling(60).mean()
            C1 = ((MA30 - L_series) / MA60) * 200

            # 2. M2指标：SMA(MAX(C-REF(C,1),0),7,1)/SMA(ABS(C-REF(C,1)),7,1)*100
            price_changes = C_series.diff()
            positive_changes = price_changes.where(price_changes > 0, 0)
            negative_changes = price_changes.abs()

            # 使用自定义SMA函数
            sma_positive = self.sma_custom(positive_changes, 7, 1)
            sma_abs = self.sma_custom(negative_changes, 7, 1)
            M2 = (sma_positive / sma_abs) * 100

            # 3. G1条件：FILTER(REF(M2,1)<20 AND M2>REF(M2,1),5)
            g1_raw_condition = (M2.shift(1) < 20) & (M2 > M2.shift(1))
            G1 = self.apply_filter(g1_raw_condition, 5)

            # 4. TU条件：C/MA(C,40)<0.74
            MA40 = C_series.rolling(40).mean()
            TU = C_series / MA40 < 0.74

            # 5. SMMA相关指标
            EMA5 = C_series.ewm(span=5).mean()
            SMMA = EMA5.ewm(span=5).mean()
            IM = EMA5 - EMA5.shift(1)
            TSMMA = SMMA - SMMA.shift(1)
            DIVMA = (EMA5 - SMMA).abs()

            # 6. TDJ条件：(H-L)/REF(C,1)>0.05
            TDJ = (H_series - L_series) / C_series.shift(1) > 0.05

            # 7. ET和TDF计算
            ET = (IM + TSMMA) / 2
            TDF = DIVMA * (ET ** 3)  # POW(DIVMA,1)*POW(ET,3)

            # 8. NTDF：TDF/HHV(ABS(TDF),5*3)
            NTDF = TDF / TDF.abs().rolling(15).max()

            # 9. YUL条件：COUNT(TDJ,5)>1
            YUL = TDJ.rolling(5).sum() > 1

            # 10. QD启动条件：TU AND TDJ AND YUL
            QD = TU & TDJ & YUL

            # 11. QR确定条件：CROSS(NTDF,-0.9)
            QR = (NTDF > -0.9) & (NTDF.shift(1) <= -0.9)

            # 12. 计算MACD
            if TALIB_AVAILABLE:
                # 使用talib计算MACD
                macd_line, macd_signal, macd_hist = talib.MACD(C_series.values)
                macd_condition = pd.Series(macd_line, index=C_series.index) > -1.5
            else:
                # 使用EMA差值计算MACD
                ema12 = C_series.ewm(span=12).mean()
                ema26 = C_series.ewm(span=26).mean()
                macd_line = ema12 - ema26
                macd_condition = macd_line > -1.5

            # 13. BD波段条件：FILTER((G1 AND C1>20 OR C>REF(C,1)) AND REF(QD,1),10)
            bd_raw_condition = ((G1 & (C1 > 20)) | (C_series > C_series.shift(1))) & QD.shift(1)
            BD = self.apply_filter(bd_raw_condition, 10)

            # 14. XG选股条件：FILTER(REF(QD,1) AND (QR OR C>REF(C,1)) AND MACD.MACD>-1.5,10)
            xg_raw_condition = QD.shift(1) & (QR | (C_series > C_series.shift(1))) & macd_condition
            XG = self.apply_filter(xg_raw_condition, 10)

            # 15. 最终妖底确定条件：COUNT(XG,13)>=1 AND BD
            XG_count = XG.rolling(13).sum() >= 1
            yaodi_condition = XG_count & BD

            # 安全检查，确保有足够的数据
            if len(yaodi_condition) == 0:
                return False

            # 检查最后一个值是否为有效值
            yaodi_signal = yaodi_condition.iloc[-1] if not pd.isna(yaodi_condition.iloc[-1]) else False

            # 调试信息（只在信号为True时输出）
            if yaodi_signal:
                self.logger.info(f"{stock} 妖底确定信号触发: TU={TU.iloc[-1]}, TDJ={TDJ.iloc[-1]}, "
                        f"YUL={YUL.iloc[-1]}, QD={QD.iloc[-1]}, QR={QR.iloc[-1]}, "
                        f"XG_count={XG_count.iloc[-1]}, BD={BD.iloc[-1]}")

            return bool(yaodi_signal)

        except Exception as e:
            self.logger.error(f"计算妖底确定信号时出错 {stock}: {e}")
            return False

    def calculate_sell_signal(self, stock: str) -> bool:
        """
        计算高抛低吸卖出信号 - 完全按照卖出选股公式实现
        :param stock: 股票代码
        :return: 是否满足卖出条件
        """
        try:
            # 获取足够的历史数据（需要75天用于VAR1计算）
            hist_data = self.data_provider.get_stock_data(stock, 100)

            if len(hist_data) < 75:
                return False

            # 转换为pandas Series便于计算
            C = pd.Series(hist_data['close'])
            H = pd.Series(hist_data['high'])
            L = pd.Series(hist_data['low'])

            # 参数设置
            N1, N2 = 21, 8

            # 1. VAR1指标：3*SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1)-2*SMA(SMA((CLOSE-LLV(LOW,75))/(HHV(HIGH,75)-LLV(LOW,75))*100,20,1),15,1)
            LLV75 = L.rolling(75).min()
            HHV75 = H.rolling(75).max()
            base_indicator = (C - LLV75) / (HHV75 - LLV75) * 100
            sma1 = self.sma_custom(base_indicator, 20, 1)
            sma2 = self.sma_custom(sma1, 15, 1)
            VAR1 = 3 * sma1 - 2 * sma2

            # 2. VAR2指标：(CLOSE-LLV(LOW,26))/(HHV(HIGH,26)-LLV(LOW,26))*100
            LLV26 = L.rolling(26).min()
            HHV26 = H.rolling(26).max()
            VAR2 = (C - LLV26) / (HHV26 - LLV26) * 100

            # 3. VAR3指标：SMA(SMA(VAR2,3,1),3,1)
            VAR3_step1 = self.sma_custom(VAR2, 3, 1)
            VAR3 = self.sma_custom(VAR3_step1, 3, 1)

            # 4. VAR4指标：EMA(VAR3,5)
            VAR4 = VAR3.ewm(span=5).mean()

            # 5. VAR5指标：LLV(LOW,26)
            VAR5 = LLV26

            # 6. VAR6指标：HHV(HIGH,34)
            VAR6 = H.rolling(34).max()

            # 7. VAR7指标：EMA((CLOSE-VAR5)/(VAR6-VAR5)*4,4)*25
            VAR7 = ((C - VAR5) / (VAR6 - VAR5) * 4).ewm(span=4).mean() * 25

            # 8. VAR8指标：(2*C+H+L)/4
            VAR8 = (2 * C + H + L) / 4

            # 9. VAR9指标：LLV(LOW,N1) = LLV(LOW,21)
            VAR9 = L.rolling(N1).min()

            # 10. VAR10指标：HHV(HIGH,N2) = HHV(HIGH,8)
            VAR10 = H.rolling(N2).max()

            # 11. VAR2W指标：100-100*(HHV(HIGH,14)-CLOSE)/(HHV(HIGH,14)-LLV(LOW,14))
            HHV14 = H.rolling(14).max()
            LLV14 = L.rolling(14).min()
            VAR2W = 100 - 100 * (HHV14 - C) / (HHV14 - LLV14)

            # 12. MW指标：EMA(VAR2W,3)
            MW = VAR2W.ewm(span=3).mean()

            # 13. VAR3W指标：EMA(VAR2W,7)
            VAR3W = VAR2W.ewm(span=7).mean()

            # 14. M1指标：EMA(VAR3W,5)
            M1 = VAR3W.ewm(span=5).mean()

            # 15. MB1条件：CROSS(MW,M1) AND M1<20
            MB1 = (MW > M1) & (MW.shift(1) <= M1.shift(1)) & (M1 < 20)

            # 16. MG1条件：IF(CROSS(M1,MW) AND REF(MW,1)>80,80 ,0)
            MG1_condition = (M1 > MW) & (M1.shift(1) <= MW.shift(1)) & (MW.shift(1) > 80)
            MG1 = MG1_condition.astype(int) * 80

            # 17. 核心指标MJ：EMA((VAR8-VAR9)/(VAR10-VAR9)*100,9)
            raw_mj = (VAR8 - VAR9) / (VAR10 - VAR9) * 100
            MJ = raw_mj.ewm(span=9).mean()

            # 18. TM指标：EMA(0.667*REF(MJ,1)+0.333*MJ,2)
            TM = (0.667 * MJ.shift(1) + 0.333 * MJ).ewm(span=2).mean()

            # 19. 卖出条件：CROSS(80,MJ) - 80从下方向上穿越MJ，即MJ从上方向下跌破80
            sell_signal = (MJ.shift(1) > 80) & (MJ <= 80)

            # 安全检查
            if len(sell_signal) == 0:
                return False

            # 获取最新的信号
            latest_signal = sell_signal.iloc[-1] if not pd.isna(sell_signal.iloc[-1]) else False

            # 调试信息（只在信号为True时输出）
            if latest_signal:
                self.logger.info(f"{stock} 卖出信号触发: MJ当前={MJ.iloc[-1]:.2f}, MJ前值={MJ.iloc[-2]:.2f}, "
                        f"VAR8={VAR8.iloc[-1]:.2f}, VAR9={VAR9.iloc[-1]:.2f}, VAR10={VAR10.iloc[-1]:.2f}")

            return bool(latest_signal)

        except Exception as e:
            self.logger.error(f"计算卖出信号时出错 {stock}: {e}")
            return False

    def get_stock_pool(self) -> List[str]:
        """
        获取股票池（全市场A股，排除北证、ST、停牌等）
        """
        try:
            # 获取所有A股股票
            stocks = self.data_provider.get_stock_list()

            # 过滤条件
            filtered_stocks = []

            for stock in stocks:
                # 排除北证股票（北证股票代码格式：8XXXXX.XBSE 或 43XXXX.XBSE）
                stock_code = stock.split('.')[0] if '.' in stock else stock[:6]
                if stock_code.startswith('8') or stock_code.startswith('43') or '.XBSE' in stock:
                    continue

                # 只保留沪深A股（.XSHE 和 .XSHG）
                if '.' in stock and not (stock.endswith('.XSHE') or stock.endswith('.XSHG')):
                    continue

                # 排除ST股票（通过股票名称判断，需要数据提供者支持）
                # 这里简化处理，实际使用时需要根据具体数据源调整
                try:
                    current_price = self.data_provider.get_current_price(stock)
                    if current_price > 0:
                        filtered_stocks.append(stock)
                except:
                    continue

            self.logger.info(f"股票池筛选完成，共 {len(filtered_stocks)} 只股票（已排除北证、ST、停牌股票）")

            # 返回全部符合条件的股票，不再限制数量
            return filtered_stocks

        except Exception as e:
            self.logger.error(f"获取股票池时出错: {e}")
            return []

    def trade_strategy(self):
        """
        主要交易策略函数
        """
        self.logger.info('开始执行交易策略')

        # 1. 检查当前持仓，执行卖出逻辑
        self.check_sell_signals()

        # 2. 执行买入逻辑（移除持仓数量限制）
        self.check_buy_signals()

        # 获取当前持仓信息
        current_holdings = self.get_holding_stocks()
        current_positions = len(current_holdings)

        self.logger.info(f'策略执行完成，当前持仓数量: {current_positions}')

    def check_sell_signals(self):
        """
        检查卖出信号
        """
        # 获取当前持仓股票
        holding_stocks = self.get_holding_stocks()

        # 对持有的股票检查卖出信号
        for stock in holding_stocks:
            try:
                if self.calculate_sell_signal(stock):
                    self.logger.info(f"触发卖出信号，全仓卖出 {stock}")
                    # 全仓卖出
                    positions = self.trade_provider.get_positions()
                    if stock in positions:
                        amount = positions[stock].get('amount', 0)
                        self.trade_provider.sell(stock, amount)
            except Exception as e:
                self.logger.error(f"检查卖出信号时出错 {stock}: {e}")

    def check_buy_signals(self):
        """
        检查买入信号（支持加仓）
        """
        # 获取股票池
        stock_pool = self.get_stock_pool()
        self.logger.info(f"股票池大小: {len(stock_pool)}")

        # 当前可用资金
        account_info = self.trade_provider.get_account_info()
        available_cash = account_info.get('available_cash', 0)
        total_value = account_info.get('total_value', available_cash)

        # 单次买入金额（十分之一仓位）
        single_position_value = total_value * self.config.position_ratio
        self.logger.info(f"可用资金: {available_cash:.2f}, 单次买入金额: {single_position_value:.2f}")

        buy_candidates = []

        # 获取当前实际持仓的股票列表
        current_holdings = self.get_holding_stocks()

        # 遍历股票池，寻找买入信号
        # 由于股票池较大，采用分批检查策略
        import random

        # 随机打乱股票池，避免总是检查相同的股票
        random.shuffle(stock_pool)

        checked_count = 0
        # 移除检查数量限制，检查整个股票池

        for stock in stock_pool:
            try:
                checked_count += 1

                # 检查妖底确定信号（包括已持有的股票，支持加仓）
                if self.calculate_yaodi_signal(stock):
                    current_price = self.data_provider.get_current_price(stock)
                    if current_price > 0:
                        # 标记是否为加仓
                        is_add_position = stock in current_holdings
                        buy_candidates.append((stock, current_price, is_add_position))
                        action_type = "加仓" if is_add_position else "买入"
                        self.logger.info(f"找到{action_type}候选: {stock}, 价格: {current_price:.2f}")

            except Exception as e:
                self.logger.error(f"检查买入信号时出错 {stock}: {e}")
                continue

        self.logger.info(f"检查了 {checked_count} 只股票，找到 {len(buy_candidates)} 个买入候选")

        # 按价格排序，优先买入低价股（可根据需要调整排序逻辑）
        buy_candidates.sort(key=lambda x: x[1])

        # 执行买入（移除数量限制）
        bought_count = 0
        for stock, price, is_add_position in buy_candidates:
            if available_cash >= single_position_value:
                action_type = "加仓" if is_add_position else "买入"
                self.logger.info(f"触发买入信号，{action_type} {stock}，价格: {price:.2f}")
                # 买入指定金额（1/10仓位）
                amount = single_position_value / price
                self.trade_provider.buy(stock, amount)
                available_cash -= single_position_value
                bought_count += 1
            else:
                self.logger.info(f"可用资金不足，跳过 {stock}")
                break

        self.logger.info(f"本次共执行 {bought_count} 笔买入/加仓操作")

    def _log_portfolio_status(self):
        """
        输出投资组合状态信息
        """
        current_time = datetime.datetime.now()
        self.logger.info(f'收盘后运行时间: {current_time.strftime("%H:%M:%S")}')

        # 输出当前持仓信息
        positions = self.trade_provider.get_positions()
        account_info = self.trade_provider.get_account_info()
        total_value = account_info.get('total_value', 0)
        available_cash = account_info.get('available_cash', 0)

        # 获取实际持仓股票
        holding_stocks = self.get_holding_stocks()

        self.logger.info(f'当前总资产: {total_value:.2f}')
        self.logger.info(f'可用资金: {available_cash:.2f}')
        self.logger.info(f'持仓数量: {len(holding_stocks)}')

        # 输出持仓详情
        for stock in holding_stocks:
            position = positions[stock]
            try:
                current_price = self.data_provider.get_current_price(stock)
                amount = position.get('amount', 0)
                avg_cost = position.get('avg_cost', 0)
                profit_loss = (current_price - avg_cost) * amount
                profit_rate = (current_price - avg_cost) / avg_cost * 100 if avg_cost > 0 else 0
                self.logger.info(f'持仓 {stock}: 数量={amount}, '
                        f'成本={avg_cost:.2f}, 现价={current_price:.2f}, '
                        f'盈亏={profit_loss:.2f} ({profit_rate:.2f}%)')
            except Exception as e:
                self.logger.error(f"获取持仓详情时出错 {stock}: {e}")

        self.logger.info('一天结束')
        self.logger.info('##############################################################')


# 示例数据提供者实现（需要根据实际数据源调整）
class ExampleDataProvider(DataProvider):
    """示例数据提供者，需要根据实际数据源实现"""

    def get_stock_data(self, symbol: str, count: int) -> pd.DataFrame:
        """获取股票历史数据 - 需要实现"""
        # 这里需要根据实际数据源实现
        # 返回包含 'close', 'high', 'low', 'volume' 列的DataFrame
        raise NotImplementedError("需要根据实际数据源实现")

    def get_current_price(self, symbol: str) -> float:
        """获取当前价格 - 需要实现"""
        # 这里需要根据实际数据源实现
        raise NotImplementedError("需要根据实际数据源实现")

    def get_stock_list(self) -> List[str]:
        """获取股票列表 - 需要实现"""
        # 这里需要根据实际数据源实现
        # 返回股票代码列表
        raise NotImplementedError("需要根据实际数据源实现")


# 示例交易提供者实现（需要根据实际交易接口调整）
class ExampleTradeProvider(TradeProvider):
    """示例交易提供者，需要根据实际交易接口实现"""

    def buy(self, symbol: str, amount: float) -> bool:
        """买入股票 - 需要实现"""
        # 这里需要根据实际交易接口实现
        raise NotImplementedError("需要根据实际交易接口实现")

    def sell(self, symbol: str, amount: float) -> bool:
        """卖出股票 - 需要实现"""
        # 这里需要根据实际交易接口实现
        raise NotImplementedError("需要根据实际交易接口实现")

    def get_positions(self) -> Dict[str, Dict]:
        """获取持仓信息 - 需要实现"""
        # 这里需要根据实际交易接口实现
        # 返回格式: {symbol: {'amount': float, 'avg_cost': float}}
        raise NotImplementedError("需要根据实际交易接口实现")

    def get_account_info(self) -> Dict:
        """获取账户信息 - 需要实现"""
        # 这里需要根据实际交易接口实现
        # 返回格式: {'available_cash': float, 'total_value': float}
        raise NotImplementedError("需要根据实际交易接口实现")


# 使用示例
if __name__ == "__main__":
    # 创建数据提供者和交易提供者实例（需要根据实际情况实现）
    data_provider = ExampleDataProvider()
    trade_provider = ExampleTradeProvider()

    # 创建策略配置
    config = StrategyConfig()

    # 创建策略实例
    strategy = YaodiStrategy(data_provider, trade_provider, config)

    # 初始化策略
    strategy.initialize()

    # 运行策略（在实际使用中，这应该在定时任务中调用）
    # strategy.run_daily_strategy()

    print("妖底确定买入高抛卖出策略 - 通用版本已创建完成")
    print("请根据您使用的数据源和交易接口实现 DataProvider 和 TradeProvider 类")
